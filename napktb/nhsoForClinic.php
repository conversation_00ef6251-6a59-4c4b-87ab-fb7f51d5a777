<?php
require '../session_setting.php';

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

ini_set('memory_limit', '1G');

if (!isset($_SESSION['user']) || empty($_SESSION['user']) || $_SESSION['user'] == null || $_SESSION['user'] == '') {
  header("Location: ../login.php?redirect=../napktb/nhsoForClinic.php");
}

require '../helpers/pagesvisited.php'; //

// error_reporting(E_ALL);
// ini_set('display_errors', 1);

if (!isset($_GET['start'])) {
  $startDate = checkQuarter()['this_month_start'];
  $endDate = date('Y-m-d');
  $query = "?start={$startDate}&end={$endDate}";
}

$select_fy = $_GET['select_fy'] ?? '2026';

$query =  (isset($_GET['start']) && isset($_GET['end'])) ? "?start={$_GET['start']}&end={$_GET['end']}&select_fy={$select_fy}" : '';

$query_ecascade =  (isset($_GET['start']) && isset($_GET['end'])) ? "?start={$_GET['start']}&end={$_GET['end']}&select_fy={$select_fy}&ecascade=true" : '?ecascade=true';

?>

<?php include '../layouts/header.php' ?>
<div class="wrapper">

  <?php include '../layouts/topmenu.php' ?>
  <?php include '../layouts/sidebarmenu.php' ?>

  <style>
    .nap_code_input.form-control {
      padding: 2px;
      height: 100%;
    }

    .nap-position-absolute {
      width: 200px !important;
      position: absolute;
      top: -20px !important;
      right: -120px !important;
    }

    .nap-position-absolute-api {
      width: 200px !important;
      position: absolute;
      top: -30px !important;
      right: -170px !important;
    }
  </style>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        NHSO บันทึก Clinic HIV ลง NAP
        <small>รายการเคส Clinic HIV Testing เพื่อบันทึกลง NAP จาก ฐานข้อมูล Clinic</small>
      </h1>
      <ol class="breadcrumb">
        <li><a href="../index.php"><i class="fas fa-tachometer-alt"></i> Home</a></li>
        <li class="">NAPPlus & KTB</li>
        <li class="active"> NHSO Clinic</li>
      </ol>
    </section>

    <!-- Main content -->
    <section class="content">
      <?php flash('invalidroute'); ?>
      <input type='hidden' value='<?= $_SESSION['user']; ?>' name='cbs' id="cbs" />
      <div class="row">
        <div class="col-lg-12">
          <div class="box box-warning">
            <div class="box-header with-border">
              <h3 class="box-title">NHSO Clinic List</h3>
              <div class="box-tools pull-right">
                <input type="text" class="input-style" id="date_range">
                <select name="select_fy" id="select_fy" class="input-select">
                  <option <?= $select_fy == '2023' ? 'selected' : '' ?>>2023</option>
                  <option <?= $select_fy == '2024' ? 'selected' : '' ?>>2024</option>
                  <option <?= $select_fy == '2025' ? 'selected' : '' ?>>2025</option>
                  <option <?= $select_fy == '2026' ? 'selected' : '' ?>>2026</option>
                </select>
                <a href="./nhsoForClinic.php<?= $query ?>" id="filter_link" class="input-style w100 btn-primary">ดึงข้อมูล</a>
                <!-- <a href="./nhsoForClinic.php?period=all" class="input-style w100 btn-danger">ทั้งหมด</a> -->
                <a href="./nhsoForClinic.php<?= $query_ecascade ?>" id="filter_link_ecascade" class="input-style w100 btn-danger">ยังไม่บันทึก NAP</a>
              </div>
            </div>
            <!-- /.box-header -->

            <!-- form start -->
            <div class="box-body">
              <pre>
  คำแนะนำ
    : ข้อมูลจะถูก List มาจากการ Clinic HIV Testing
    : ปุ่ม "ยังไม่บันทึก NAP" มุมบนขวา จะแสดงเฉพาะรายการเคสที่ยังไม่ได้บันทึก NAP เท่านั้นเพื่อดูยอดคงค้าง
    : สามารถเลือกช่วงเวลา และ กดดึงข้อมูล ได้
    : แต่ละช่องรายงาน สามารถกดปุ่ม เพื่อ copy ข้อมูล
    : เมื่อบันทึก NAP แล้ว นำรหัส NAP Testing บันทึกลงในช่อง NAP_Code หากมีหมายเหตุเพิ่มเติมให้ระบุช่อง NAP_Comment จากนั้นกด checkbox ช่อง NAP_Action เพื่อบันทึก
    : หากรายการไหนไม่สามารถบันทึก NAP ได้ พิมพ์ช่อง NAP_Code ด้วยตัวอักษร 4 ตัว เช่น xxxx, 0000, 9999 และ ระบุหมายเหตุที่บันทึกไม่ได้ จากนั้นกด checkbox ช่อง NAP_Action เพื่อบันทึก
    : การบันทึก NAP Testing ใช้เพื่อเบิกจ่าย ตามระบบ สปสช โปรดบันทึกทุกรายให้ถูกต้อง
</pre>
              <div class="row">
                <div class="col-lg-12">

                  <hr>
                  <div>
                    <h5 class="text-bold text-primary">แสดง/ซ่อน Column</h5>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="0">No</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="1">ที่มา</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="2">ที่มา</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="3">Type</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="4">Source</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="5">CBS</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="6">UIC</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="7">UIC</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="8">count</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="9">KP</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="10">KP</button> -->
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="11">Sub KP</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="12">Firstname</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="13">Lastname</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="14">Phone</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="15">Service_Date</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="16">ระยะ (วัน)</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="17">อาชีพ</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="18">สิทธิรักษา</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="19">HIV</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="20">Syphilis</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="21">HCV</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="22">CT</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="23">NG</button>
                    <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="24">ID_Card</button>
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="25">ID_Card</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="26">nap_code</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="27">nap_comment</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="28">NAP_Code</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="29">NAP_Comment</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="30">NAP_Action</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="31">NAP_Staff</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="32">NAP_Date</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="33">Source_ID</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="34">BirthDay</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="35">thisRowId</button> -->
                    <!-- <button class="btn btn-sm btn-default col-toggle" onClick="button_toggle(event)" data-column="36">location</button> -->
                  </div>
                  <hr>

                  <div class="table-responsive">
                    <table id="nhsoListTable" class="table text-center display table-hover">
                      <thead>
                        <tr>
                          <th>No</th>
                          <th>ที่มา</th>
                          <th>ที่มา</th>
                          <th>Type</th>
                          <th>Source</th>
                          <th>CBS</th>
                          <th>UIC</th>
                          <th>UIC</th>
                          <th>count</th>
                          <th>KP</th>
                          <th>KP</th>
                          <th>Sub KP</th>
                          <th>Firstname</th>
                          <th>Lastname</th>
                          <th>Phone</th>
                          <th>Service_Date</th>
                          <th>ระยะ (วัน)</th>
                          <th>อาชีพ</th>
                          <th>สิทธิรักษา</th>
                          <th>HIV</th>
                          <th>Syphilis</th>
                          <th>HCV</th>
                          <th>CT</th>
                          <th>NG</th>
                          <th id="id_card">ID_Card</th>
                          <th>ID_Card</th>
                          <th>nap_code</th>
                          <th>nap_comment</th>
                          <th style='background:#ffe3a8;border:1px solid #EEE;text-align:center;'>NAP_VCT</th> <!-- 30 -->
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Code</th>
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Comment</th>
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Action</th>
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Staff</th>
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Date</th>
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Action</th>
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Status</th>
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Staff</th>
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Date</th>
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Message</th>
                          <th>Source_ID</th>
                          <th>BirthDay</th>
                          <th>thisRowId</th>
                          <th>location</th>
                          <th>nhso_id</th> <!-- 41 -->
                        </tr>
                      </thead>
                      <tfoot>
                        <tr>
                          <th>No</th> <!-- 0 -->
                          <th>ที่มา</th> <!-- 1 -->
                          <th>ที่มา</th> <!-- 2 -->
                          <th>Type</th> <!-- 3 -->
                          <th>Source</th> <!-- 4 -->
                          <th>CBS</th> <!-- 5 -->
                          <th>UIC</th> <!-- 6 -->
                          <th>UIC</th> <!-- 7 -->
                          <th>count</th> <!-- 8 -->
                          <th>KP</th> <!-- 9 -->
                          <th>KP</th> <!-- 10 -->
                          <th>Sub KP</th> <!-- 11 -->
                          <th>Firstname</th> <!-- 12 -->
                          <th>Lastname</th> <!-- 13 -->
                          <th>Phone</th> <!-- 14 -->
                          <th>Service_Date</th> <!-- 15 -->
                          <th>ระยะ (วัน)</th> <!-- 16 -->
                          <th>อาชีพ</th> <!-- 17 -->
                          <th>สิทธิรักษา</th> <!-- 18 -->
                          <th>HIV</th> <!-- 19 -->
                          <th>Syphilis</th> <!-- 20 -->
                          <th>HCV</th> <!-- 21 -->
                          <th>CT</th> <!-- 22 -->
                          <th>NG</th> <!-- 23 -->
                          <th>ID_Card</th> <!-- 24 -->
                          <th>ID_Card</th> <!-- 25 -->
                          <th>nap_code</th> <!-- 26 -->
                          <th>nap_comment</th> <!-- 27 -->
                          <th style='background:#ffe3a8;border:1px solid #EEE;text-align:center;'>NAP_VCT</th> <!-- 28 -->
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Code</th> <!-- 29 -->
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Comment</th> <!-- 30 -->
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Action</th> <!-- 31 -->
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Staff</th> <!-- 32 -->
                          <th style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>NAP_Date</th> <!-- 33 -->
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Action</th> <!-- 34 -->
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Status</th> <!-- 35 -->
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Staff</th> <!-- 36 -->
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Date</th> <!-- 37 -->
                          <th style='background:#519cff;border:1px solid #EEE;text-align:center;'>API_Message</th> <!-- 38 -->
                          <th>Source_ID</th> <!-- 39 -->
                          <th>BirthDay</th> <!-- 40 -->
                          <th>thisRowId</th> <!-- 41 -->
                          <th>location</th> <!-- 42 -->
                          <th>nhso_id</th> <!-- 43 -->
                        </tr>
                      </tfoot>
                      <tbody>
                        <?php
                        $no = 0;
                        $kpList = ['MSM', 'MSW', 'TG', 'TGM', 'TGSW', 'PWID-Male', 'PWID-Female', 'FSW', 'Male', 'Female'];

                        // 
                        $eCascadeUnDone = false;
                        $period = 'all';

                        if (isset($_GET['start'])) $eCascadeUnDone = false;

                        $startDate = $_GET['start'];
                        $endDate = $_GET['end'] . " 23:59:59";

                        if (!isset($_GET['start'])) $startDate = checkQuarter()['this_month_start'];
                        if (!isset($_GET['end'])) $endDate = date('Y-m-d') . " 23:59:59";

                        if ($_GET['period'] == 'all') {
                          $startDate = '2014-10-01';
                          $endDate = date('Y-m-d H:i:s');
                        }

                        if (isset($_GET['ecascade']) && $_GET['ecascade'] == true) {
                          $eCascadeUnDone = true;
                        }

                        $check_fy = $_GET['select_fy'] ?? '2026';

                        $datefilter = selectDatabase("nhso_config", [], ["year" => ["=", checkQuarter($check_fy)['this_fy']]])->fetch()->nhso_clinic_start ?? '2021-07-27';

                        $start_query = checkQuarter($check_fy)['q1_start'];
                        $end_query = checkQuarter($check_fy)['q4_end'];

                        $clinicDatas = caseClinicForNap($conn, $eCascadeUnDone, $period, $check_fy, $datefilter, $start_query, $end_query);

                        $filter_data = array_filter($clinicDatas, function ($item) use ($startDate, $endDate) {
                          return $item['service_date'] >= $startDate && $item['service_date'] < $endDate;
                        });

                        if ($eCascadeUnDone) {
                          $filter_data = array_filter($filter_data, fn($item) => !$item['nap_code'] || $item['nap_code'] === '');
                        }

                        $data_count = count($filter_data);

                        if ($data_count > 5000) {
                          $toomuch = 'hidden';
                          echo "<label class='alert alert-danger'>ข้อมูลมากเกินไปที่จะแสดง ($data_count) โปรดกด Export Excel</label>";
                        }

                        foreach ($filter_data as $key => $item) {

                          if (in_array($item['kp'], $kpList) && !in_array(substr($item['id_card'], 0, 1), [0, 6, 7])) {
                            // if (!in_array($item['uic'], $uicLists)) {

                            $source = '';
                            $kp = '';

                            $no++;

                            $service_date = dateOnly($item['service_date']);
                            $nap_staff = $item['nap_staff'];
                            $nap_date = dateOnly($item['nap_date']);

                            $id_card = checkPID($item['id_card']) ? labelbig($item['id_card'], 'success') : labelbig($item['id_card'], 'danger');

                            $source = $item['source'] == 'testing' ? labelbig('Clinic', 'maroon') : $source;

                            $kp = labelbig($item['kp']);
                            $kp = $item['kp'] == 'MSM' ? labelbig($item['kp'], 'primary') : $kp;
                            $kp = $item['kp'] == 'MSW' ? labelbig($item['kp'], 'teal') : $kp;
                            $kp = $item['kp'] == 'TG' ? labelbig($item['kp'], 'warning') : $kp;
                            $kp = $item['kp'] == 'TGSW' ? labelbig($item['kp'], 'purple') : $kp;
                            $kp = $item['kp'] == 'FSW' ? labelbig($item['kp'], 'maroon') : $kp;
                            $kp = $item['kp'] == 'TGM' ? labelbig($item['kp'], 'danger') : $kp;

                            $nap_status = $item['nap_status'] == 'true' ? 'checked' : '';
                            $readonly = $item['nap_status'] == 'true' ? 'readonly' : '';
                            $readonlyColor = $item['nap_status'] == 'true' ? '#DDD' : '#FFF';
                            $disabled = $item['nap_status'] == 'true' ? '' : 'disabled';

                            $vct_nap_status = $item['vct_nap_status'] == 'true' ? 'checked' : '';

                            $input_id = 'Clinic_' . $item['source'] . '_' . $item['id'] . '_' . $item['id_card'];
                            $checkBoxId = $input_id . '_checkbox';
                            $commentId = $input_id . '_cmd';
                            $service_date_copy = thaidate($item['service_date'], '/');

                            $hiv_result = testColor($item['hiv_result']);
                            $sti_result = testColor($item['sti_result']);

                            $ct_result = testColor($item['ct_result']);
                            $ng_result = testColor($item['ng_result']);

                            $hcv = '-';
                            if ($item['hcv_result'] == 'Anti-HCV Non-reactive' || $item['hcv_result'] == 'Anti-HCV Negative') $hcv = 'N';
                            if ($item['hcv_result'] == 'Anti-HCV Reactive' || $item['hcv_result'] == 'Anti-HCV Positive') $hcv = 'R';
                            if ($item['hcv_result'] == 'Anti-HCV Invalie') $hcv = 'I';
                            $hcv_result = testColor($hcv);

                            $type = labelbig($item['clinic_type'], 'primary');
                            if ($item['clinic_type'] == 'DIC') $type = labelbig($item['clinic_type'], 'teal');

                            $recruit_array = $item['cbs'] ? explode(',', $item['cbs']) : ['', ''];

                            $recruit_type = '';
                            $recruit_cbs = '';

                            if ($recruit_array[0] != 'walk in') {
                              $recruit_type = $recruit_array[0];
                              $recruit_cbs = $recruit_array[1];
                            }

                            if ($item['cbs'] == 'walk in') {
                              $recruit_type = 'walk in';
                              $recruit_cbs = '';
                            }

                            if ($recruit_type == 'reach') $recruit_type = labelbig($recruit_type, 'info', '1.2rem');
                            if ($recruit_type == 'retain') $recruit_type = labelbig($recruit_type, 'maroon', '1.2rem');
                            if ($recruit_type == 'walk in') $recruit_type = labelbig($recruit_type, 'purple', '1.2rem');

                            $sub_kp = $item['sub_kp'] ? implode(',', json_decode($item['sub_kp'])) : '';

                            $id_card_id = 'id_card_' . $input_id;

                            // NAP Lab Testing API
                            $disabled_api = $nap_status === 'checked' ? '' : 'disabled';
                            $checkBoxId_api = $input_id . '_checkbox_api';

                            $nap_status_api = $item['nap_api_status'] ? 'checked' : '';

                            $nap_result_api = $item['nap_api_status'] ?? '';
                            $nap_staff_api = $item['nap_api_staff'] ?? '';
                            $nap_date_api = $item['nap_api_date'] ? dateOnly($item['nap_api_date']) : '';
                            $nap_comment_api = $item['nap_api_status'] === 'Tested' ? 'ส่งข้อมูล NAP Test แล้ว' : 'ส่งข้อมูล API ไม่สำเร็จ';
                            if (!$item['nap_api_status']) $nap_comment_api = '';

                            echo "
                              <tr class='{$toomuch}'>
                                  <td class='text-center'>$no</td>
                                  <td class='text-center'>{$source}</td>
                                  <td>{$item['source']}</td>
                                  <td>{$type}</td>
                                  <td>{$recruit_type}</td>
                                  <td>{$recruit_cbs}</td>
                                  " . tdCopy($item['uic'], $item['uic'], '', 'text-center', 'nowrap') . "
                                  <td>{$item['uic']}</td>
                                  <td>{$item['uic_count']}</td>
                                  <td>{$kp}</td>
                                  <td>{$item['kp']}</td>
                                  <td>{$sub_kp}</td>
                                  " . tdCopy($item['firstname'], $item['firstname'], '', 'text-center', 'nowrap') . "
                                  " . tdCopy($item['lastname'], $item['lastname'], '', 'text-center', 'nowrap') . "
                                  " . tdCopy($item['phone'], $item['phone'], '', 'text-center', 'nowrap') . "
                                  " . tdCopy($service_date_copy, $service_date, '', 'text-center', 'nowrap') . "
                                  <td>{$item['formLastTest']}</td>
                                  <td>{$item['occupation']}</td>
                                  <td>{$item['healthcare']}</td>
                                  <td>{$hiv_result}</td>
                                  <td>{$sti_result}</td>
                                  <td>{$hcv_result}</td>
                                  <td>{$ct_result}</td>
                                  <td>{$ng_result}</td>
                                  " . tdCopy($item['id_card'], $id_card, '', 'text-center', "nowrap id='$id_card_id'") . "
                                  <td>{$item['id_card']}</td>
                                  <td>{$item['nap_code']}</td>
                                  <td>{$item['nap_comment']}</td>
                                  <td class='text-center' style='background:#ffe3a8;border:1px solid #EEE;text-align:center;'>
                                    <div style='position: relative;' id='vct_{$input_id}_checkbox_container'>
                                      <div class='mt-ios' style='font-size:7px;'> 
                                      <input id='vct_check_{$input_id}' type='checkbox' onclick='updateVCTNapStatus(this)' {$vct_nap_status}/>
                                      <label style='margin:0px;' for='vct_check_{$input_id}'></label>
                                      </div>
                                    </div>
                                  </td>
                                  <td style='background:#dbfff4;border:1px solid #EEE;text-align:center;'><input {$readonly} type='text' id='{$input_id}' class='text-center nap_code_input form-control' style='width:185px;background:{$readonlyColor};' value='{$item['nap_code']}' oninput='updateNapCode(this)'></td>
                                  <td style='background:#dbfff4;border:1px solid #EEE;text-align:center;'><input {$readonly} type='text' id='{$commentId}' class='text-center nap_code_input form-control' style='width:100px;background:{$readonlyColor};' value='{$item['nap_comment']}' oninput=''></td>
                                  <td class='text-center' style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>
                                    <div style='position: relative;' id='{$input_id}_checkbox_container'>
                                      <div class='mt-ios' style='font-size:7px;'> 
                                      <input {$disabled} id='{$checkBoxId}' type='checkbox' onclick='updateNapStatus(this)' {$nap_status}/>
                                      <label style='margin:0px;' for='{$checkBoxId}'></label>
                                      </div>
                                    </div>
                                  </td>
                                  <td style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>{$nap_staff}</td>
                                  <td style='background:#dbfff4;border:1px solid #EEE;text-align:center;'>{$nap_date}</td>

                                  <td class='text-center' style='background:#519cff;border:1px solid #EEE;text-align:center;'>
                                    <div style='position: relative;' id='{$input_id}_checkbox_container_api'>
                                      <div class='mt-ios' style='font-size:7px;'> 
                                      <input {$disabled_api} id='{$checkBoxId_api}' type='checkbox' onclick='updateNapAPI(this)' {$nap_status_api}/>
                                      <label style='margin:0px;' for='{$checkBoxId_api}'></label>
                                      </div>
                                    </div>
                                  </td>
                                  <td style='background:#519cff;border:1px solid #EEE;text-align:center;'>{$nap_result_api}</td>
                                  <td style='background:#519cff;border:1px solid #EEE;text-align:center;'>{$nap_staff_api}</td>
                                  <td style='background:#519cff;border:1px solid #EEE;text-align:center;'>{$nap_date_api}</td>
                                  <td style='background:#519cff;border:1px solid #EEE;text-align:center;'>{$nap_comment_api}</td>
                                  
                                  <td class='text-center'>{$item['id']}</td>
                                  <td class='text-center'>{$item['birth_day']}</td>
                                  <td class='text-center'>{$input_id}</td>
                                  <td class='text-center'>{$item['location']}</td>
                                  <td class='text-center'>{$item['nhso_id']}</td>
                                </tr> ";
                            // }
                          }
                        }
                        ?>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>
        <!-- modal -->
        <div class="modal modal-gray fade" id="modal-default" tabindex="-1">
          <div class="modal-dialog">
            <div class="modal-content ">

            </div>
          </div>
        </div>
        <!-- /modal -->
      </div>
      <floating-scroll-buttons
        table-id="nhsoListTable"
        section1-id="id_card"
        section1-icon="fa-list">
      </floating-scroll-buttons>
    </section>
    <!-- /.content -->
  </div>
  <!-- /.content-wrapper -->

  <?php include '../layouts/footer.php' ?>
  <?php // include '../layouts/controlslidebar.php' 
  ?>

  <!-- /.control-sidebar -->
  <div class="control-sidebar-bg"></div>
</div>
<!-- ./wrapper -->

<?php include '../layouts/javascript.php' ?>
<script src="../dist/js/vue/scrollButtons.js?v=<?= date("YmdHi"); ?>"></script>
<script>
  var selected = [];

  const currentDate = new Date();
  // Setup - add a text input to each footer cell
  $('#nhsoListTable tfoot th').each(function() {
    var title = $(this).text();
    if (title != '') {
      $(this).html('<input type="text" style="width:100%" />');
    } else {
      $(this).html('');
    }
    $('[data-toggle="popover"]').popover();
  });

  var table = $('#nhsoListTable').DataTable({
    // sDom: 'C<"clear">lfrtip',
    autoWidth: false,
    pageLength: 10,
    paging: true,
    columnDefs: [{
      "targets": [2, 7, 10, 25, 26, 27, 41, 43],
      "visible": false,
      "searchable": false,
    }, ],
    order: [
      [33, 'asc'],
      [0, 'desc']

    ],
    dom: 'Bfrtip',
    "buttons": [{
        extend: 'excel',
        text: 'Excel',
        title: null, // ✅ ไม่ใส่ title แถวแรก
        filename: 'clinic_enter_nap_export_' + currentDate.getFullYear() + '_' + ("0" + (currentDate.getMonth() + 1)).slice(-2) + '_' + ("0" + currentDate.getDate()).slice(-2) + '_' + ("0" + currentDate.getHours()).slice(-2) + ("0" + currentDate.getMinutes()).slice(-2) + ("0" + currentDate.getSeconds()).slice(-2),
      },
      'pageLength'
      // 'copy', 'excel', 'print', 'pageLength'
    ],
  });

  $('#nhsoListTable tbody').on('click', 'tr', function() {
    var id = this.id;
    var index = $.inArray(id, selected);

    if (index === -1) {
      selected.push(id);
    } else {
      selected.splice(index, 1);
    }

    $(this).toggleClass('selected');
  });

  // Apply the search
  table.columns().every(function() {
    var that = this;

    $('input', this.footer()).on('keyup change', function() {
      if (that.search() !== this.value) {
        that
          .search(this.value)
          .draw();
      }
    });
  });

  $('#nhsoListTable tfoot tr').appendTo('#nhsoListTable thead');

  function updateNapCode(e) {

    if (e.value.length > 3) {
      $(`#${e.id}_checkbox`).prop('disabled', false)
    } else {
      $(`#${e.id}_checkbox`).prop('disabled', true)
    }

  }

  async function updateNapAPI(e) {
    const COL = {
      status: 35,
      staff: 36,
      date: 37,
      note: 38,
      caseId: 41,
      nhsoId: 43
    };

    // ------- helpers -------
    const findRowByCheckboxId = (checkboxId) => {
      // เดินทุก row หา case_id ที่ประกอบเป็น *_checkbox_api ตรงกับ e.id
      let found = null;
      table.rows().every(function(rowIdx) {
        const row = this.data();
        const caseId = row[COL.caseId];
        if (`${caseId}_checkbox_api` === checkboxId) {
          found = {
            rowIdx,
            row
          };
          return false; // break
        }
      });
      return found;
    };

    const setCells = (rowIdx, {
      status = '',
      staff = '',
      date = '',
      note = ''
    } = {}) => {
      table.cell(rowIdx, COL.status).data(status);
      table.cell(rowIdx, COL.staff).data(staff);
      table.cell(rowIdx, COL.date).data(date);
      table.cell(rowIdx, COL.note).data(note);
      table.draw('page');
    };

    const showToast = ({
      target,
      icon,
      title,
      timer = 3000
    }, didClose) => {
      Swal.fire({
        position: 'top-end',
        target,
        icon,
        title,
        customClass: {
          container: 'nap-position-absolute-api'
        },
        showConfirmButton: false,
        toast: true,
        timer,
        didClose,
      });
    };

    const postForm = async (url, payloadObj) => {
      const body = new URLSearchParams(payloadObj);
      const res = await fetch(url, {
        method: 'POST',
        body
      });
      // ป้องกัน JSON parse error
      const text = await res.text();
      try {
        return JSON.parse(text);
      } catch {
        throw new Error('INVALID_JSON');
      }
    };
    // ------- /helpers -------

    // หาแถวจาก checkbox ที่กด
    const found = findRowByCheckboxId(e.id);
    if (!found) return; // ไม่พบแถว — เงียบ ๆ
    const {
      rowIdx,
      row
    } = found;

    const case_id = row[COL.caseId];
    const nhso_id = row[COL.nhsoId];
    const nap_code = $(`#${case_id}`).val();
    const nap_staff = $('#userName').text() || '';
    const nap_date = moment().format('YYYY-MM-DD HH:mm:ss');
    const targetSel = `#${case_id}_checkbox_container_api`;

    // ถ้า uncheck ให้เคลียร์ค่าแล้วจบ
    if (!e.checked) {
      setCells(rowIdx);
      return;
    }

    const commonPayload = {
      nhso_id,
      nap_code,
      nap_staff
    };

    // 1) get_lab_request
    let data_req;
    try {
      data_req = await postForm('../services/ajaxNHSO_NAP_api_get_lab_request.php', commonPayload);
    } catch (err) {
      showToast({
          target: targetSel,
          icon: 'error',
          title: 'เชื่อมต่อ API ไม่ได้',
          timer: 3000
        },
        () => {
          setCells(rowIdx);
          e.checked = false;
        },
      );
      return;
    }

    // กรณี 1: result = 0 (error ฝั่ง NAP)
    if (data_req.RESULT === '0') {
      showToast({
          target: targetSel,
          icon: 'error',
          title: data_req.RESULT_MESSAGE || 'NAP API Error'
        },
        () => {
          setCells(rowIdx);
          e.checked = false;
        },
      );
      return;
    }

    // กรณี 2: result = 1 แต่ไม่พบข้อมูล LAB_REQUEST
    const hasLabRequest = Array.isArray(data_req.LAB_REQUEST) && data_req.LAB_REQUEST.length > 0;
    if (data_req.RESULT === '1' && !hasLabRequest) {
      const labId = data_req?.HEADER?.LAB_REQUEST_ID || nap_code || '-';
      showToast({
          target: targetSel,
          icon: 'warning',
          title: `LAB_REQUEST_ID: '${labId}' ไม่ถูกต้อง`
        },
        () => {
          setCells(rowIdx);
          e.checked = false;
        },
      );
      return;
    }

    // กรณี 3: result = 1 และมี LAB_REQUEST -> set_lab_result
    let data_set;
    try {
      data_set = await postForm('../services/ajaxNHSO_NAP_api_set_lab_result.php', commonPayload);
    } catch (err) {
      showToast({
          target: targetSel,
          icon: 'error',
          title: 'ส่งผลไป NAP ไม่สำเร็จ',
          timer: 2000
        },
        () => setCells(rowIdx, {
          status: 'Failed',
          staff: nap_staff,
          date: nap_date,
          note: 'ส่งข้อมูล API ไม่สำเร็จ',
        }),
      );
      return;
    }

    if (data_set.RESULT_MESSAGE === 'Success') {
      showToast({
          target: targetSel,
          icon: 'success',
          title: 'NAP API Success',
          timer: 2000
        },
        () => setCells(rowIdx, {
          status: 'Tested',
          staff: nap_staff,
          date: nap_date,
          note: 'ส่งข้อมูล NAP Test แล้ว',
        }),
      );
    } else {
      showToast({
          target: targetSel,
          icon: 'error',
          title: data_set.RESULT_MESSAGE || 'NAP API Error',
          timer: 2000
        },
        () => setCells(rowIdx, {
          status: 'Failed',
          staff: nap_staff,
          date: nap_date,
          note: 'ส่งข้อมูล API ไม่สำเร็จ',
        }),
      );
    }
  }

  function updateVCTNapStatus(e) {

    let dataForUpdate = [];

    table.rows().data().filter(data => {
      if (e.id == `vct_check_${data[41]}`) {
        dataForUpdate = data
      }
    })

    if (dataForUpdate.length > 0) {

      let case_id = dataForUpdate[41]

      const source = dataForUpdate[2]
      const source_id = dataForUpdate[39]
      const uic = dataForUpdate[7]
      const kp = dataForUpdate[11]
      const id_card = $(`#id_card_${case_id}`).text()
      const vct_nap_status = e.checked
      // const nap_code = $(`#${case_id}`).val()
      // const nap_comment = $(`#${case_id}_cmd`).val()
      // const nap_status = e.checked
      // const nap_staff = $('#userName').text()
      // const nap_date = moment().format("YYYY-MM-DD HH:mm:ss")
      const input_id = case_id

      const data = {
        year: $("#select_fy").val(),
        type: 'Clinic',
        source: source,
        source_id: source_id,
        uic: uic,
        kp: kp,
        id_card: id_card,
        vct_nap_status: vct_nap_status,
        // nap_code: nap_code,
        // nap_comment: nap_comment,
        // nap_status: nap_status,
        // nap_staff: nap_staff,
        // nap_date: nap_date,
        input_id: input_id,
        nap_type: 'VCT'
      };

      console.log(data)

      $.ajax({
        url: '../services/ajaxNHSO_NAP.php',
        type: 'POST',
        data: data,
        success: function(res_nap) {

          const response = JSON.parse(res_nap)

          console.log(response)

          table.row(dataForUpdate[0] - 1).every((rowIdx) => {
            if (response.data.vct_nap_status === 'true' || response.data.vct_nap_status === true) {

              // sweet alert
              Swal.fire({
                position: 'top-end',
                target: `#vct_${input_id}_checkbox_container`,
                icon: 'success',
                title: 'บันทึก VCT แล้ว',
                customClass: {
                  container: 'nap-position-absolute'
                },
                showConfirmButton: false,
                toast: true,
                timer: 2000
              })

            }
          })
        }
      });
    }
  }

  function updateNapStatus(e) {

    let dataForUpdate = [];

    table.rows().data().filter(data => {

      if (e.id == data[41] + '_checkbox') {
        dataForUpdate = data
      }
    })

    if (dataForUpdate.length > 0) {

      let case_id = dataForUpdate[41]

      const source = dataForUpdate[2]
      const source_id = dataForUpdate[39]
      const uic = dataForUpdate[7]
      const kp = dataForUpdate[11]
      const id_card = $(`#id_card_${case_id}`).text()
      const nap_code = $(`#${case_id}`).val()
      const nap_comment = $(`#${case_id}_cmd`).val()
      const nap_status = e.checked
      const nap_staff = $('#userName').text()
      const nap_date = moment().format("YYYY-MM-DD HH:mm:ss")
      const input_id = case_id

      const data = {
        year: $("#select_fy").val(),
        type: 'Clinic',
        source: source,
        source_id: source_id,
        uic: uic,
        kp: kp,
        id_card: id_card,
        vct_nap_status: true,
        nap_code: nap_code,
        nap_comment: nap_comment,
        nap_status: nap_status,
        nap_staff: nap_staff,
        nap_date: nap_date,
        input_id: input_id
      };

      $.ajax({
        url: '../services/ajaxNHSO_NAP.php',
        type: 'POST',
        data: data,
        success: function(res_nap) {

          const response = JSON.parse(res_nap)

          table.row(dataForUpdate[0] - 1).every((rowIdx) => {
            if (response.data.nap_status === 'true' || response.data.nap_status === true) {

              $(`#vct_check_${case_id}`).prop('checked', true);

              // sweet alert
              Swal.fire({
                position: 'top-end',
                target: `#${input_id}_checkbox_container`,
                icon: 'success',
                title: 'บันทึกสำเร็จ',
                customClass: {
                  container: 'nap-position-absolute'
                },
                showConfirmButton: false,
                toast: true,
                timer: 2000
              })

              table.cell(rowIdx, 32).data(nap_staff).draw('page')
              table.cell(rowIdx, 33).data(nap_date).draw('page')
              table.cell(rowIdx, 43).data(response.nhso_id).draw('page')

              $(`#${response.data.input_id}, #${response.data.input_id}_cmd`).prop('readonly', true)
              $(`#${response.data.input_id}, #${response.data.input_id}_cmd`).css('background', '#DDD')

              // nap api
              $(`#${e.id}_api`).prop('disabled', false)
              $(`#${e.id}_api`).prop('checked', false)

              // check nhso_nap_request

              const request_data = {
                nhso_id: response.nhso_id,
                nap_code: nap_code,
                nap_staff: nap_staff,
                check_nap_test: true
              };

              // get lab request
              $.ajax({
                url: '../services/ajaxNHSO_NAP_api_get_lab_result.php',
                type: 'POST',
                data: request_data,
                success: function(res_result) {

                  const data_result = JSON.parse(res_result)

                  if (
                    data_result &&
                    data_result.LAB_REQUEST_ID === nap_code
                  ) {

                    Swal.fire({
                      title: `NAP LAB API!`,
                      html: `ตรวจพบข้อมูล บันทึก Lab <br> (<b>${nap_code}</b>) <br> ในระบบ NAP แล้ว!`,
                      icon: "success",
                      position: "top",
                      // customClass: 'swal-wide',
                      // timer: 1000,
                      willClose: () => {
                        $(`#${e.id}_api`).prop('checked', true)

                        table.cell(rowIdx, 35).data('Tested').draw('page')
                        table.cell(rowIdx, 36).data(nap_staff).draw('page')
                        table.cell(rowIdx, 37).data(nap_date).draw('page')
                        table.cell(rowIdx, 38).data('ลงข้อมูล NAP Test แล้ว').draw('page')
                      },
                    });

                  }
                }
              })
              // check nhso_nap_request

            } else {
              table.cell(rowIdx, 32).data('').draw('page')
              table.cell(rowIdx, 33).data('').draw('page')
              table.cell(rowIdx, 43).data('').draw('page')

              $(`#${response.data.input_id}`).val('')
              $(`#${response.data.input_id}_checkbox`).prop('disabled', true)
              $(`#${response.data.input_id}, #${response.data.input_id}_cmd`).prop('readonly', false)
              $(`#${response.data.input_id}, #${response.data.input_id}_cmd`).css('background', '#FFF')

              // nap api
              $(`#${e.id}_api`).prop('disabled', true)
              $(`#${e.id}_api`).prop('checked', false)

              table.cell(rowIdx, 35).data('').draw('page')
              table.cell(rowIdx, 36).data('').draw('page')
              table.cell(rowIdx, 37).data('').draw('page')
              table.cell(rowIdx, 38).data('').draw('page')
            }
          })
        }
      });
    }
  }

  let default_start = moment().startOf('month').format('YYYY-MM-DD')
  let default_end = moment().format('YYYY-MM-DD')

  if (getRequests().period == 'all') {
    default_start = '2014-10-01'
    default_end = moment().format('YYYY-MM-DD HH:mm:ss')
  }

  if (getRequests().start) default_start = getRequests().start
  if (getRequests().end) default_end = getRequests().end

  $("#select_fy").on('change', (e) => {

    console.log(e.target.value)

    let select_fy = e.target.value
    if (e.target.value == '') select_fy = '2026'

    // let filter_link = $("#filter_link").attr('href')
    // let filter_link_ecascade = $("#filter_link_ecascade").attr('href')

    // if (!getRequests().start) {
    //   filter_link += `?start=${default_start}&end=${default_end}`
    //   filter_link_ecascade += `&start=${default_start}&end=${default_end}`
    // }

    // filter_link += `&select_fy=${select_fy}`
    // filter_link_ecascade += `&select_fy=${select_fy}`

    let filter_link = `?start=${default_start}&end=${default_end}&select_fy=${select_fy}`
    let filter_link_ecascade = `?start=${default_start}&end=${default_end}&ecascade=true&select_fy=${select_fy}`

    $("#filter_link").attr("href", filter_link.toString());
    $("#filter_link_ecascade").attr("href", filter_link_ecascade.toString());

  })

  $(`#date_range`).flatpickr({
    mode: "range",
    // minDate: "today",
    dateFormat: "Y-m-d",
    defaultDate: [default_start, default_end],

    plugins: [
      ShortcutButtonsPlugin({
        button: [{
            label: "Month"
          },
          {
            label: "Quarter"
          },
          {
            label: "FY"
          },
          {
            label: "last FY"
          },
        ],
        // label: "or",
        onClick: (index, fp) => {

          let start = moment().startOf('month').format('YYYY-MM-DD')
          let end = moment().endOf('month').format('YYYY-MM-DD')

          if (index == 0) {
            start = moment().startOf('month').format('YYYY-MM-DD')
            end = moment().endOf('month').format('YYYY-MM-DD')
          }

          if (index == 1) {
            start = moment().startOf('quarter').format('YYYY-MM-DD')
            end = moment().endOf('quarter').format('YYYY-MM-DD')
          }

          if (index == 2) {
            start = moment().startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            end = moment().endOf('year').subtract(3, 'months').format('YYYY-MM-DD')

            if (moment().month() > 8) {
              start = moment().add(1, 'y').startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
              end = moment().add(1, 'y').endOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            }
          }

          if (index == 3) {
            start = moment().subtract(1, 'y').startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            end = moment().subtract(1, 'y').endOf('year').subtract(3, 'months').format('YYYY-MM-DD')

            if (moment().month() > 8) {
              start = moment().startOf('year').subtract(3, 'months').format('YYYY-MM-DD')
              end = moment().endOf('year').subtract(3, 'months').format('YYYY-MM-DD')
            }
          }

          fp.setDate([start, end]);
          $('#filter_link').attr("href", `./nhsoForClinic.php?start=${start}&end=${end}&select_fy=${$("#select_fy").val()}`);
          $('#filter_link_ecascade').attr("href", `./nhsoForClinic.php?start=${start}&end=${end}&ecascade=true&select_fy=${$("#select_fy").val()}`);
        }
      })
    ],

    // enable: [
    //   {
    //     from: moment().subtract(7, 'days').format('YYYY-MM-DD'),
    //     to: moment().add(7, 'd').format('YYYY-MM-DD')
    //   },
    // ],
    onChange: (selectedDates, dateStr, instance) => {
      if (selectedDates.length == 2) {

        let start = moment(selectedDates[0]).format('YYYY-MM-DD')
        let end = moment(selectedDates[1]).format('YYYY-MM-DD')

        $('#filter_link').attr("href", `./nhsoForClinic.php?start=${start}&end=${end}&select_fy=${$("#select_fy").val()}`);
        $('#filter_link_ecascade').attr("href", `./nhsoForClinic.php?start=${start}&end=${end}&ecascade=true&select_fy=${$("#select_fy").val()}`);
        // update input for start_date and end_date
        // this.fetchTestMeNowLists(moment(selectedDates[0]).format('YYYY-MM-DD'), moment(selectedDates[1]).format('YYYY-MM-DD'))
      }
    }
  });

  function button_toggle(e) {

    if (e.target.classList.contains("btn-default")) {
      e.target.classList.remove("btn-default")
      e.target.classList.add("btn-primary")
    } else {
      e.target.classList.remove("btn-primary")
      e.target.classList.add("btn-default")
    }

    var column = table.column(e.target.dataset.column);

    // Toggle the visibility
    column.visible(!column.visible());
  }
</script>
<?php include '../layouts/end.php' ?>
