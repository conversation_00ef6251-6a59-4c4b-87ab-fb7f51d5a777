<?php
$directoryURI = $_SERVER['REQUEST_URI'];
$path = parse_url($directoryURI, PHP_URL_PATH);
$components = explode('/', $path);
$first_part = $components[1];

$avatar = $_SESSION['avatar'] ?? 'dist/img/user_placeholder.jpeg';
?>

<!-- Left side column. contains the logo and sidebar -->
<aside class="main-sidebar">
  <!-- sidebar: style can be found in sidebar.less -->
  <section class="sidebar">
    <!-- Sidebar user panel -->
    <div class="user-panel">
      <div class="pull-left image">
        <img src="../showImage.php?file=<?= $avatar ?>" onerror="this.onerror=null; this.src='../showImage.php?file=dist/img/user_placeholder.jpeg';" style="height: 70px; border-radius: 50%" alt="User Image">
      </div>
      <div class="pull-left info">
        <p id="userName"><?= $_SESSION['user']; ?></p>
        <p id="userCode" class="hidden"><?= $_SESSION['user_code']; ?></p>
        <p class="hidden" id="siteName"><?= $_SESSION['site_specific']->sitename2; ?></p>
        <p class="hidden" id="orgName"><?= $_SESSION['site_specific']->sitename2; ?></p>
        <p class="hidden" id="orgCode"><?= $_SESSION['site_specific']->sitename14; ?></p>
        <?php if ($_SESSION['user_signature']) {
          $reference = $_SESSION['user_signature'];
        } else {
          $reference = generateRandomString(6);
        } ?>
        <a href="#"><i class="fa fa-circle text-success"></i> Online</a>
        <a class="btn btn-primary btn-sm" href="../userprofile/userprofile.php" style="margin-top: -10px; padding: 5px 10px;">หน้า Profile</a><br>
      </div>
    </div>
    <!-- search form -->
    <!-- <form action="#" method="get" class="sidebar-form">
          <div class="input-group">
            <input type="text" name="q" class="form-control" placeholder="Search...">
            <span class="input-group-btn">
              <button type="submit" name="search" id="search-btn" class="btn btn-flat">
                <i class="fa fa-search"></i>
              </button>
            </span>
          </div>
        </form> -->
    <!-- /.search form -->
    <!-- sidebar menu: : style can be found in sidebar.less -->
    <ul class="sidebar-menu" data-widget="tree">
      <li class="treeview <?php if ($first_part == "documentation") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
        <a href="#" class="<?= userHasCode(15) ? 'hidden' : '' ?>">
          <i class="fa fa-book"></i>
          <span>คู่มือการใช้งาน / Update</span>
          <span class="pull-right-container">
            <span class="label label-danger pull-right">New</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/documentation/documentation_main.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../documentation/documentation_main.php"><i class="fa fa-book"></i> หน้าคู่มือ</a></li>
          <li class="<?php if ($directoryURI == "/documentation/documentation_main.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../documentation/update_history.php"><i class="fa fa-code"></i> ประวัติการอัพเดท</a></li>
        </ul>
      </li>

      <li class="header">MAIN NAVIGATION</li>
      <li class="treeview <?php if ($first_part == "services") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
        <a href="#" class="<?= userHasCode(15) ? 'hidden' : '' ?>">
          <i class="fa fa-list-ul"></i>
          <span>Services</span>
          <span class="pull-right-container">
            <span class="label label-success pull-right">R R T T P R</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/services/reach.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../services/reach.php"><i class="fa fa-search"></i> Reach</a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/clinic.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/clinic.php"><i class="fas fa-stethoscope" style="margin-right:5px;"></i> Clinic</a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?= $_SESSION['site_specific']->sitename2 == "ACTSE" ? "" : "hidden " ?> <?php if ($directoryURI == "/services/late_lab_result.php") {
                                                                                                                                                                echo "active";
                                                                                                                                                              } else {
                                                                                                                                                                echo "noactive";
                                                                                                                                                              } ?>"><a href="../services/late_lab_result.php"><i class="fas fa-flask" style="margin-right:5px;"></i> บันทึกผล Lab หลังปิดคลินิก</a></li>
          <li class="<?php if ($directoryURI == "/services/recruit.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../services/recruit.php"><i class="fa fa-handshake-o"></i> Recruit<span class="pull-right-container"><span class="hidden label label-success pull-right">A1</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/registerToB1.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/registerToB1.php"><i class="fa fa-address-card" style="margin-right:5px;"></i> Register To B1<span class="pull-right-container"><span class="hidden label label-warning pull-right">B1</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/clinic_history_lists.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/clinic_history_lists.php"><i class="fas fa-stethoscope" style="margin-right:5px;"></i> Clinic To C1<span class="pull-right-container"><span class="hidden label label-danger pull-right">C1</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?>"><a target="_blank" href="../services/clinicQuestionnaire.php"><i class="fas fa-question" style="margin-right:5px;"></i> แบบประเมินคลินิก</a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/search.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/addCareCase.php" class="btn btnleft"><i class="fa fa-plus"></i> เพิ่ม/แก้ไขเคสบวก</a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/preClinicCaseInfo.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/preClinicCaseInfo.php"><i class="fa fa-address-card" style="margin-right:5px;"></i> ข้อมูลเคส Register</a></li>
          <li class="<?php if ($directoryURI == "/services/addpm.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../services/addpm.php" class="btn btnleft"><i class="fa fa-plus"></i> ADD PM</a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/nhsoList.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/nhsoList.php" class="btn btnleft"><i class="fa fa-plus"></i> NHSO สปสช</a></li>
          
          <!-- <li class="<?php if ($directoryURI == "/http/checkID.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>"><a href="../http/checkID.php" class="btn btnleft"><i class="fa fa-flask"></i> Check ID</a></li> -->
          <!-- <li class="<?php if ($directoryURI == "/services/treat.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>"><a href="../services/treat.php" class="btn disabled btnleft"><i class="fa fa-medkit"></i> Treat</a></li> -->
          <!-- <li class="<?php if ($directoryURI == "/services/prevention.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>"><a href="../services/prevention.php" class="btn disabled btnleft"><i class="fa fa-tint"></i> Prevention</a></li> -->
          <!-- <li class="<?php if ($directoryURI == "/services/retain.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>"><a href="../services/retain.php" class="btn disabled btnleft"><i class="fa fa-recycle"></i> Retain</a></li> -->
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/pharmacy.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/pharmacy.php" class="btn btnleft"><i class="fa fa-list-ul"></i> Pharmacy</a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/services/pharmacy.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../services/history_edit_prep.php" class="btn btnleft"><i class="fa fa-layer-group"></i>ประวัติแก้ไข PrEP</a></li>
        </ul>
      </li>

      <li class="treeview <?php if ($first_part == "napktb") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
        <a href="#" class="<?= userHasCode(15) ? 'hidden' : '' ?>">
          <i class="fa fa-university"></i>
          <span>NAPPlus & KTB</span>
          <span class="pull-right-container">
            <span class="label label-info pull-right">KTB</span>
            <span class="label label-purple pull-right">NAP</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="hidden <?php if ($directoryURI == "/napktb/autoNapPlus.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../napktb/autoNapPlus.php" class="btn btnleft"><i class="fa fa-magic"></i> Auto NAP+™<span class="pull-right-container"><span class="label label-warning pull-right">Beta</span></span></a></li>
          <li class="<?php if ($directoryURI == "/napktb/nhsoForReach.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../napktb/nhsoForReach.php" class="btn btnleft"><i class="fa fa-plus"></i>Reach To NAP<span class="pull-right-container"><span class="label label-purple pull-right">NAP</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/napktb/nhsoForClinic.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../napktb/nhsoForClinic.php" class="btn btnleft"><i class="fa fa-plus"></i>Clinic HIV To NAP<span class="pull-right-container"><span class="label label-purple pull-right">NAP</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/napktb/nhsoForClinicSyphilis.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>"><a href="../napktb/nhsoForClinicSyphilis.php" class="btn btnleft"><i class="fa fa-plus"></i>Clinic Syphilis To KTB<span class="pull-right-container"><span class="label label-info pull-right">KTB</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/napktb/nhsoForCD4.php") {
            echo "active";
          } else {
            echo "noactive";
          } ?>"><a href="../napktb/nhsoForCD4.php" class="btn btnleft"><i class="fa fa-plus"></i>CD4 To NAP<span class="pull-right-container"><span class="label label-purple pull-right">NAP</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/napktb/nhsoForVL.php") {
            echo "active";
          } else {
            echo "noactive";
          } ?>"><a href="../napktb/nhsoForVL.php" class="btn btnleft"><i class="fa fa-plus"></i>VL To NAP<span class="pull-right-container"><span class="label label-purple pull-right">NAP</span></span></a></li>
          <li class="<?= userHasCode(2) || !userHasRoles([2,3,4,5,6]) ? "hidden" : "" ?> <?php if ($directoryURI == "/napktb/nhsoForHCV.php") {
            echo "active";
          } else {
            echo "noactive";
          } ?>"><a href="../napktb/nhsoForHCV.php" class="btn btnleft"><i class="fa fa-plus"></i>HCV To KTB<span class="pull-right-container"><span class="label label-info pull-right">KTB</span></span></a></li>
        </ul>
      </li>

      <li id="dashboard" class="treeview <?php if ($first_part == "dashboard") {
                                            echo "active";
                                          } else {
                                            echo "noactive";
                                          } ?>">
        <a href="#">
          <i class="fa fa-area-chart"></i>
          <span>Dashboard</span>
          <span class="pull-right-container">
            <span class="label label-primary pull-right">Dashboard</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?= userHasCode(15) ? 'hidden' : '' ?> <?php if ($directoryURI == "/dashboard/main.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>">
            <a href="../dashboard/main.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> Main
            </a>
          </li>
          <li class="<?= userHasCode(15) ? 'hidden' : '' ?> <?php if ($directoryURI == "/dashboard/dashboard.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>">
            <a href="../dashboard/dashboard.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> Dashboard
            </a>
          </li>
          <li class="<?= userHasCode(15) ? 'hidden' : '' ?> <?php if ($directoryURI == "/dashboard/dashboard2.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>">
            <a href="../dashboard/dashboard2.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> Dashboard 2
            </a>
          </li>
          <li class="<?= userHasCode(15) ? 'hidden' : '' ?> <?php if ($directoryURI == "/dashboard/dashboard3.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>">
            <a href="../dashboard/dashboard3.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> PrEP Dashboard
            </a>
          </li>
          <li class="<?= userHasCode(15) ? '' : 'hidden' ?> <?php if ($directoryURI == "/dashboard/fhi360.php") {
                                                                                      echo "active";
                                                                                    } else {
                                                                                      echo "noactive";
                                                                                    } ?>">
            <a href="../dashboard/fhi360.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> FHI360 Dashboard
            </a>
          </li>
          <li class="<?= !userHasCode(20) ? 'hidden' : '' ?>">
            <a href="../dashboard/indextesting.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> Index Testing Dashboard
            </a>
          </li>
        </ul>
      </li>

      <li id="report" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "report") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <i class="fa fa-area-chart"></i>
          <span>Report</span>
          <span class="pull-right-container">
            <span class="label label-success pull-right">Report</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/report/cliniclog.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/cliniclog.php">
              <i class="fa fa-key"></i> Clinic Summary
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/clinicCaseSummary.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/clinicCaseSummary.php">
              <i class="fa fa-key"></i> สรุปบริการคลินิกรายเคส
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/clinic_operation_time.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/clinic_operation_time.php">
              <i class="fa fa-key"></i> สรุปเวลาคลินิกรายเคส
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/emwtools_summary.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/emwtools_summary.php">
              <i class="fa fa-list"></i> สรุปผล E-mw Tools
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/report/medicalClinicLog.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../report/medicalClinicLog.php">
              <i class="fa fa-key"></i> Medical Clinic Summary
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/loginlog.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/loginlog.php">
              <i class="fa fa-key"></i> Login Log
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/questionnaireSummary.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/questionnaireSummary.php">
              <i class="fa fa-question"></i> Questionnaire Summary
            </a>
          </li>
          <li class="hidden <?= $_SESSION['site_specific']->sitename2 != "CAREMAT" ? "hidden " : "" ?><?php if ($directoryURI == "/report/birthDaySMS.php") {
                                                                                                        echo "active";
                                                                                                      } else {
                                                                                                        echo "noactive";
                                                                                                      } ?>">
            <a href="../report/birthDaySMS.php">
              <i class="fa fa-key"></i> Birth Day SMS log
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/report/smsNegativeRetainLog.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../report/smsNegativeRetainLog.php">
              <i class="fa fa-key"></i> SMS Negative Retain Log
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/searchlog.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/searchlog.php">
              <i class="fa fa-search-plus"></i> Search Log
            </a>
          </li>
          <li class="<?= $_SESSION['site_specific']->sitename2 != "CAREMAT" ? "hidden " : "" ?><?php if ($directoryURI == "/report/addCareCaseLog.php") {
                                                                                                  echo "active";
                                                                                                } else {
                                                                                                  echo "noactive";
                                                                                                } ?>">
            <a href="../questionnaire/report.php">
              <i class="fa fa-list"></i> HIV Risk Check Report
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/report/testmenowtoken.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../report/testmenowtoken.php">
              <i class="fa fa-search-plus"></i> TestMeNow Token
            </a>
          </li>
          <li class="<?= userHasRoles(6) ? '' : 'hidden '?><?= $directoryURI == '/report/logging_monitor.php' ? 'active' : 'noactive'?>">
            <a href="../report/logging_monitor.php">
              <i class="fa fa-history"></i> Monitor Notify Log
            </a>
          </li>
          <li class="<?= $directoryURI == '/report/logging_staff.php' ? 'active' : 'noactive'?>">
            <a href="../report/logging_staff.php">
              <i class="fa fa-history"></i> Staff Notify Log
            </a>
          </li>
          <li class="<?= $directoryURI == '/report/logging_prep_request.php' ? 'active' : 'noactive'?>">
            <a href="../report/logging_prep_request.php">
              <i class="fa fa-history"></i> PrEP Request Notify Log
            </a>
          </li>
          <li class="<?= userHasRoles([3,4,5]) ? '' : 'hidden '?><?= $directoryURI == '/report/print_lab_log.php' ? 'active' : 'noactive'?>">
            <a href="../report/print_lab_log.php">
              <i class="fa fa-print"></i> Lab Result Print Log
            </a>
          </li>
        </ul>
      </li>
      <li id="export" class="<?= userHasSiteName(["namkwan", "vcap", "hugfang", "mplus_bkk"]) && $_SESSION['user_level'] !== 'admin' ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "export") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <i class="fa fa-database"></i>
          <span>Export</span>
          <span class="pull-right-container">
            <span class="label label-success pull-right">Export</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/export/export_reach.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../export/export_reach.php">
              <i class="fa fa-export"></i> Export Reach
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?>">DOCUMENT</li>
      <li id="document" class="treeview <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if ($first_part == "document") {
                                                                                                              echo "active";
                                                                                                            } else {
                                                                                                              echo "noactive";
                                                                                                            } ?>">
        <a href="#">
          <i class="fa fa-book"></i>
          <span>Document</span>
          <span class="pull-right-container">
            <span class="label label-danger pull-right">Document</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/document/clinicSummary.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../document/clinicSummary.php">
              <i class="fa fa-book"></i> สรุปบริการคลินิก (Clinic Summary)
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/document/reachform.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?><?= $_SESSION['user'] == "อภิวัฒน์" ? " " : " hidden " ?>">
              <a href="../document/reachform.php">
                <i class="fa fa-book"></i> การเข้าถึง (Reach)
              </a>
            </li> -->
          <li class="<?php if ($directoryURI == "/document/retainform.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../document/retainform.php">
              <i class="fa fa-book"></i> การติดตาม (Retain)
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/document/initCareForm.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?><?= $_SESSION['user'] == "อภิวัฒน์" ? " " : " hidden " ?>">
              <a href="../document/initCareForm.php">
                <i class="fa fa-book"></i> ยินยอมการดูแลรักษา (Consent)
              </a>
            </li> -->
          <li class="<?php if ($directoryURI == "/document/caseFileCover.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?> <?= $_SESSION['user'] == "อภิวัฒน์" ? " " : " hidden " ?>">
            <a href="../document/caseFileCover.php">
              <i class="fa fa-book"></i> ใบปิดหน้าแฟ้ม (Case Profile Cover)
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode(15) ? "hidden" : "" ?>">RETENTION</li>
      <li id="retain" class="treeview <?= userHasCode(15) ? "hidden" : "" ?> <?php if ($first_part == "retain") {
                                                                                                        echo "active";
                                                                                                      } else {
                                                                                                        echo "noactive";
                                                                                                      } ?>">
        <a href="#">
          <i class="fa fa-book"></i>
          <span>Retain</span>
          <span class="pull-right-container">
            <span class="label label-primary pull-right">ติดตามเคสลบ</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/retain/negativeRetain.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../retain/negativeRetain.php">
              <i class="fa fa-book"></i> Retain Negative
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/retain/negativeRetainList.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../retain/negativeRetainList.php">
              <i class="fa fa-book"></i> Retain Negative History<span class="pull-right-container"><span class="label label-success pull-right">A1</span></span>
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/retain/questionnaireRetain.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../retain/questionnaireRetain.php">
              <i class="fa fa-book"></i> ตารางส่ง SMS แบบสอบถาม
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/retain/makeappointment.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
              <a href="../retain/makeappointment.php">
                <i class="fa fa-book"></i> Make Appointment
              </a>
            </li> -->
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?>">INDEX TESTING</li>
      <li id="index" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "indexTesting") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <i class="fa fa-book"></i>
          <span>INDEX (TT&T)</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="hidden <?php if ($directoryURI == "/indexTesting/addNewIndex.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../indexTesting/addNewIndex.php">
              <i class="fa fa-plus"></i> เพิ่ม/แก้ไข ข้อมูล
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/indexTesting/seederList.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../indexTesting/seederList.php">
              <i class="fa fa-book"></i> Index/ Seed List
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/indexTesting/partnerList.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../indexTesting/partnerList.php">
              <i class="fa fa-book"></i> Partner List
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?>">PrEP Module</li>
      <li id="prep" class="treeview <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if ($first_part == "prep") {
                                                                                                          echo "active";
                                                                                                        } else {
                                                                                                          echo "noactive";
                                                                                                        } ?>">
        <a href="#">
          <i class="fas fa-capsules fa-spin" style="margin-right:5px;"></i>
          <span>PrEP</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/prep/prep_refill.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prep/prep_refill.php">
              <i class="fas fa-ambulance" style="margin-right:5px;"></i> จ่าย PrEP นอกระบบคลินิก
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/prep/prep_dashboard.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?> hidden">
            <a href="../prep/prep_dashboard.php">
              <i class="fas fa-chart-line" style="margin-right:5px;"></i> PrEP Summary
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/prep/prep_hospital_request.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prep/prep_hospital_request.php">
              <i class="fas fa-hospital" style="margin-right:5px;"></i></i> CBO แจ้งขออนุมัติยา PrEP
            </a>
          </li>
          <li class="<?= ($directoryURI == "/prep/prep_hospital_enterlab.php") ? "active" : "noactive" ?> <?= userHasCode(13) ? "" : " hidden"; ?>">
            <a href="../prep/prep_hospital_enterlab.php">
              <i class="fas fa-hospital" style="margin-right:5px;"></i></i> PrEP รายการแจ้งตรวจเลือด
            </a>
          </li>
          <li class="<?= ($directoryURI == "/prep/prep_hospital_approve.php") ? "active" : "noactive" ?> <?= userHasCode(11) ? "" : " hidden"; ?>">
            <a href="../prep/prep_hospital_approve.php">
              <i class="fas fa-hospital" style="margin-right:5px;"></i></i> PrEP แพทย์อนุมัติยา
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/prep/prep_lists.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prep/prep_lists.php">
              <i class="fas fa-list-ul" style="margin-right:5px;"></i> PrEP Profile Lists
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/prep/prep_enter_nap.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prep/prep_enter_nap.php">
              <i class="fas fa-list-ul" style="margin-right:5px;"></i> PrEP Visit Enter NAP
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/prep/prep_enter_lab.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prep/prep_enter_lab.php">
              <i class="fas fa-list-ul" style="margin-right:5px;"></i> PrEP Enter Out-Lab
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/prep/prep_stock.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prep/prep_stock.php">
              <i class="fas fa-layer-group" style="margin-right:5px;"></i> PrEP Stock
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/prep/prevention_stock.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../prep/prevention_stock.php">
              <i class="fas fa-layer-group" style="margin-right:5px;"></i> Prevention Stock
            </a>
          </li>
          <li class="<?= $directoryURI == "/dashboard/dashboard3.php" ? "active" : "noactive" ?>">
            <a href="../dashboard/dashboard3.php">
              <i class="fas fa-tachometer-alt" style="margin-right:5px;"></i> PrEP Dashboard
            </a>
          </li>
          <li class="<?= userHasSiteName(["rsat_bkk"]) ? '' : 'hidden' ?> <?= $directoryURI == "/prep/bangrak_request_approve.php" ? "active" : "noactive" ?>">
            <a href="../prep/bangrak_request_approve.php">
              <i class="fas fa-user-md" style="margin-right:5px;"></i> Bangrak Request/Approve
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?>">PEP Module</li>
      <li id="pep" class="treeview <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if ($first_part == "pep") {
                                                                                                        echo "active";
                                                                                                      } else {
                                                                                                        echo "noactive";
                                                                                                      } ?>">
        <a href="#">
          <i class="fas fa-tablets fa-spin" style="margin-right:5px;"></i>
          <span>PEP</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <!-- <li class="<?php if ($directoryURI == "/pep/pep_refill.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
                <a href="../pep/pep_refill.php">
                <i class="fas fa-ambulance" style="margin-right:5px;"></i> จ่าย PEP นอกระบบคลินิก
              </a>
            </li> -->
          <!-- <li class="<?php if ($directoryURI == "/pep/pep_dashboard.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?> hidden">
                <a href="../pep/pep_dashboard.php">
                <i class="fas fa-chart-line" style="margin-right:5px;"></i> PEP Summary
              </a>
            </li> -->
          <li class="<?php if ($directoryURI == "/pep/pep_hospital_request.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../pep/pep_hospital_request.php">
              <i class="fas fa-hospital" style="margin-right:5px;"></i></i> CBO แจ้งขออนุมัติยา PEP
            </a>
          </li>
          <!-- <li class="<?= ($directoryURI == "/pep/pep_hospital_enterlab.php") ? "active" : "noactive" ?> <?= userHasCode(13) ? "" : " hidden"; ?>">
                <a href="../pep/pep_hospital_enterlab.php">
                <i class="fas fa-hospital" style="margin-right:5px;"></i></i> PEP รายการแจ้งตรวจเลือด
              </a>
            </li> -->
          <!-- <li class="<?= ($directoryURI == "/pep/pep_hospital_approve.php") ? "active" : "noactive" ?> <?= userHasCode(11) ? "" : " hidden"; ?>">
                <a href="../pep/pep_hospital_approve.php">
                <i class="fas fa-hospital" style="margin-right:5px;"></i></i> PEP แพทย์อนุมัติยา
              </a>
            </li> -->
          <li class="<?php if ($directoryURI == "/pep/pep_lists.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../pep/pep_lists.php">
              <i class="fas fa-list-ul" style="margin-right:5px;"></i> PEP Profile Lists
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/pep/pep_enter_nap.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
                <a href="../pep/pep_enter_nap.php">
                <i class="fas fa-list-ul" style="margin-right:5px;"></i> PEP Visit Enter NAP
              </a>
            </li> -->
          <li class="<?php if ($directoryURI == "/pep/pep_enter_lab.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../pep/pep_enter_lab.php">
              <i class="fas fa-list-ul" style="margin-right:5px;"></i> PEP Enter Out-Lab
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/pep/pep_stock.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../pep/pep_stock.php">
              <i class="fas fa-layer-group" style="margin-right:5px;"></i> PEP Stock
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode(15) ? "hidden" : "" ?>">HIVST Module</li>
      <li id="pep" class="treeview <?= userHasCode(15) ? "hidden" : "" ?> <?php if ($first_part == "hivst") {
                                                                                                      echo "active";
                                                                                                    } else {
                                                                                                      echo "noactive";
                                                                                                    } ?>">
        <a href="#">
          <i class="fas fa-medkit" style="margin-right:5px;"></i>
          <span>HIVST Module</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/hivst/hivst_management.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../hivst/hivst_management.php">
              <i class="fas fa-tasks" style="margin-right:5px;"></i></i> บริหารชุดตรวจ HIVST
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?>">Hormones Module</li>
      <li id="hormones" class="treeview <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if ($first_part == "hormones") {
                                                                                                              echo "active";
                                                                                                            } else {
                                                                                                              echo "noactive";
                                                                                                            } ?>">
        <a href="#">
          <i class="fas fa-flask" style="margin-right:5px;"></i>
          <!-- <i class="fas fa-capsules fa-spin" style="margin-right:5px;"></i> -->
          <span>Hormones</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class=hidden "<?php if ($directoryURI == "/hormones/hormones_lists.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../hormones/hormones_lists.php">
              <i class="fas fa-list" style="margin-right:5px;"></i> Hormones Profile Lists
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/hormones/hormones_profile.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../hormones/hormones_profile.php">
              <i class="fas fa-ambulance" style="margin-right:5px;"></i> Hormones Profile Lists
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/hormones/late_lab_result_hormones.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../hormones/late_lab_result_hormones.php"><i class="fas fa-flask" style="margin-right:5px;"></i> บันทึกผล Lab Hormones</a></li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?>">บันทึก Lab ย้อนหลัง</li>
      <li id="lab" class="treeview <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if ($first_part == "lab") {
                                                                                                        echo "active";
                                                                                                      } else {
                                                                                                        echo "noactive";
                                                                                                      } ?>">
        <a href="#">
          <i class="fas fa-flask" style="margin-right:5px;"></i>
          <!-- <i class="fas fa-capsules fa-spin" style="margin-right:5px;"></i> -->
          <span>บันทึก Lab ย้อนหลัง</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/lab/late_lab_result_ctng.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../lab/late_lab_result_ctng.php"><i class="fas fa-flask" style="margin-right:5px;"></i> Lab NG/CT</a></li>
          <li class="<?php if ($directoryURI == "/lab/late_lab_result_hiv_cd4.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../lab/late_lab_result_hiv_cd4.php"><i class="fas fa-flask" style="margin-right:5px;"></i> Lab CD4</a></li>
          <li class="<?php if ($directoryURI == "/lab/late_lab_result_hiv_vl.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../lab/late_lab_result_hiv_vl.php"><i class="fas fa-flask" style="margin-right:5px;"></i> Lab HIV-VL</a></li>
          <li class="<?php if ($directoryURI == "/lab/late_lab_result_else.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>"><a href="../lab/late_lab_result_else.php"><i class="fas fa-flask" style="margin-right:5px;"></i> Lab อื่นๆ (upload file)</a></li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">PREVENTION</li>
      <li id="config" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "config") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <!-- <i class="fa-regular fa-shield" style="margin-right:5px;"></i> -->
          <i class="fas fa-user-shield" style="margin-right:5px;"></i>
          <span>Prevention</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/prevention/prevention_stock.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../prevention/prevention_stock.php">
              <i class="fa fa-plus-square"></i> Stock อุปกรณ์ป้องกัน
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">Poly Clinic Module</li>
      <li id="services" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                    echo "";
                                                                                                  } else {
                                                                                                    echo "hidden ";
                                                                                                  } ?> treeview <?php if ($first_part == "services") {
                                                                                                                  echo "active";
                                                                                                                } else {
                                                                                                                  echo "noactive";
                                                                                                                } ?>">
        <a href="#">
          <i class="fas fa-solid fa-hospital" style="padding-right: 10px;"></i>
          <span>สหคลินิก</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/services/doctor_counseling_list.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../services/doctor_counseling_list.php">
              <i class="fas fa-solid fa-pills" style="padding-right: 10px;"></i> รายการแพทย์
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/services/pharmacy_list.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../services/pharmacy_list.php">
              <i class="fas fa-solid fa-pills" style="padding-right: 10px;"></i> รายการห้องยา
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/config/stock_medicines.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../services/stock_medicines.php">
              <i class="fas fa-solid fa-pills" style="padding-right: 10px;"></i> รายการยา
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">CONFIGURATION</li>
      <li id="config" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "config") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <i class="fas fa-cog fa-spin" style="margin-right:5px;"></i>
          <span>Config</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <!-- <li class="<?php if ($directoryURI == "/config/lineconfig.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
              <a href="../config/lineconfig.php">
                <i class="fa fa-bell"></i> Line Notify
              </a>
            </li> -->
          <li class="<?= !userHasCode(19) ? "hidden" : "" ?> <?php if ($directoryURI == "/config/userConfig.php") {
                                                                                        echo "active";
                                                                                      } else {
                                                                                        echo "noactive";
                                                                                      } ?>">
            <a href="../config/userConfig.php">
              <i class="fa fa-user"></i> กำหนดบทบาทเจ้าหน้าที่
            </a>
          </li>
          <li class="<?= !userHasCode(19) ? "hidden" : "" ?> <?php if ($directoryURI == "/config/change_uic.php") {
                                                                                        echo "active";
                                                                                      } else {
                                                                                        echo "noactive";
                                                                                      } ?>">
            <a href="../config/change_uic.php">
              <i class="fa fa-user"></i> เปลี่ยน UIC ในระบบ
            </a>
          </li>
          <li class="<?= !userHasCode(19) ? "hidden" : "" ?> <?php if ($directoryURI == "/config/backup_files.php") {
                                                                                        echo "active";
                                                                                      } else {
                                                                                        echo "noactive";
                                                                                      } ?>">
            <a href="../config/backup_files.php">
              <i class="fa fa-user"></i> Database backup
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/userprofile/userprofile.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../userprofile/userprofile.php ">
              <i class="fa fa-user"></i>User Profile
            </a>
          </li>
          <li class="<?= !userHasCode(19) ? "hidden" : "" ?> <?php if ($directoryURI == "/userprofile/createuser.php") {
                                                                                        echo "active";
                                                                                      } else {
                                                                                        echo "noactive";
                                                                                      } ?>">
            <a href="../userprofile/createuser.php">
              <i class="fa fa-user"></i> Create & List User
            </a>
          </li>
          <li class="<?= !userHasCode(19) ? "hidden" : "" ?> <?php if ($directoryURI == "/config/exportDatabaseJS.php") {
                                                                                        echo "active";
                                                                                      } else {
                                                                                        echo "noactive";
                                                                                      } ?>">
            <a href="../config/exportDatabaseJS.php">
              <i class="fa fa-database"></i> Export Database
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/config/incentive.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../config/incentive.php">
              <i class="fa fa-database"></i> Incentive
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/config/clinic_config.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../config/clinic_config.php">
              <i class="fa fa-plus-square"></i> Clinic Config
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/config/testkit_setting.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../config/testkit_setting.php">
              <i class="fa fa-plus-square"></i> Test Kit Config
            </a>
          </li>
          <li class="<?= !in_array($_SESSION['user'], ['']) ? "" : "hidden " ?>">
            <a target="_blank" href="https://database.actse-clinic.com/adminer.php">
              <i class="fa fa-database"></i> จัดการ Database
            </a>
          </li>
          <li class="<?= !in_array($_SESSION['user'], ['']) ? "" : "hidden " ?>">
            <a target="_blank" href="../change_password.php">
              <i class="fa fa-key"></i> เปลี่ยน Password
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">SMS</li>
      <li id="credit" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "credit") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <i class="fa fa-commenting" style="margin-right:5px;"></i>
          <span>Credit SMS</span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/credit/credit_sms.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../credit/credit_sms.php">
              <i class="fa fa-paper-plane" aria-hidden="true"></i>เพิ่ม Credit SMS
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/credit/autosendsms.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../credit/autosendsms.php">
              <i class="fa fa-book" aria-hidden="true"></i>ตั้งค่า SMS Automation
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/credit/send_sms_page.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../credit/send_sms_page.php">
              <i class="fa fa-book" aria-hidden="true"></i>ส่ง SMS ด้วยตนเอง
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/credit/check_sms_payment.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../credit/check_sms_payment.php">
              <i class="fa fa-list" aria-hidden="true"></i>รายการชำระ SMS
            </a>
          </li>
        </ul>
      </li>
      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">INDICATOR Report Data</li>
      <li id="indicator" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                      echo "";
                                                                                                    } else {
                                                                                                      echo "hidden ";
                                                                                                    } ?> treeview <?php if ($first_part == "indicator") {
                                                                                                                    echo "active";
                                                                                                                  } else {
                                                                                                                    echo "noactive";
                                                                                                                  } ?>">
        <a href="#">
          <i class="fas fa-star" style="margin-right:5px;"></i>
          <span>Indicator</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/indicator/kp_prev.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a target="_blank" href="../indicator/kp_prev.php">
              <i class="fa fa-bell"></i> KP_PREV
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/indicator/hts_tst.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a target="_blank" href="../indicator/hts_tst.php">
              <i class="fa fa-bell"></i> HTS_TST
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/indicator/prep.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a target="_blank" href="../indicator/prep.php">
              <i class="fa fa-bell"></i> PrEP
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?php if (in_array($_SESSION['user'], ["อภิวัฒน์"])) {
                          echo "";
                        } else {
                          echo "hidden ";
                        } ?>">MARATHON</li>
      <li id="marathon" class="<?php if (in_array($_SESSION['user'], ["อภิวัฒน์"])) {
                                  echo "";
                                } else {
                                  echo "hidden ";
                                } ?> treeview <?php if ($first_part == "marathon") {
                                                echo "active";
                                              } else {
                                                echo "noactive";
                                              } ?>">
        <a href="#">
          <i class="fas fa-running" style="margin-right:5px;"></i>
          <span> MARATHON 2018</span>
          <span class="pull-right-container">
            <!-- <span class="label label-danger pull-right">Retain Negative</span> -->
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/marathon/marathon2018.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../marathon/marathon2018.php">
              <i class="fas fa-ticket-alt" style="margin-right:5px;"></i>manage online ticket
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/marathon/index.html") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../marathon/">
              <i class="fa fa-book"></i> marathon home
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">CARE & SUPPORT</li>
      <li id="caresupport" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                        echo "";
                                                                                                      } else {
                                                                                                        echo "hidden ";
                                                                                                      } ?> treeview <?php if ($first_part == "caresupport") {
                                                                                                                      echo "active";
                                                                                                                    } else {
                                                                                                                      echo "noactive";
                                                                                                                    } ?>">
        <a href="#">
          <i class="fa fa-area-chart"></i>
          <span>Care & Support</span>
          <span class="pull-right-container">
            <span class="label label-success pull-right">Care & Support</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?= !in_array($_SESSION['user'], []) ? "" : " hidden "; ?><?php if ($directoryURI == "/caresupport/addCareCaseLog.php") {
                                                                                  echo "active";
                                                                                } else {
                                                                                  echo "noactive";
                                                                                } ?>">
            <a href="../caresupport/addCareCaseLog.php">
              <i class="fa fa-search-plus"></i> addCareCase Log
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/hiv.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/hiv.php">
              <i class="fa fa-plus"></i> รายการ HIV
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/stisList.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/stisList.php">
              <i class="fa fa-plus"></i> รายการ Syphilis
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/hcv_lists.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/hcv_lists.php">
              <i class="fa fa-plus"></i> รายการ HCV (ตับ C)
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/ctng_lists.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/ctng_lists.php">
              <i class="fa fa-plus"></i> รายการ หนองใน (CT/NG)
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/caresupport/ct_lists.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
            <a href="../caresupport/ct_lists.php">
              <i class="fa fa-plus"></i> รายการ หนองในเทียม (CT)
            </a>
          </li> -->
          <li class="<?php if ($directoryURI == "/caresupport/wet_smear_lists.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/wet_smear_lists.php">
              <i class="fa fa-plus"></i> รายการ พยาธิ (Wet Smear)
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/gram_stain_lists.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/gram_stain_lists.php">
              <i class="fa fa-plus"></i> รายการ หนองใน (Gram's Stain)
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/caresupport/positiveRetainList.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../caresupport/positiveRetainList.php">
              <i class="fa fa-list-ul"></i> List ประวัติการติดตาม HIV<span class="pull-right-container"><span class="hidden label label-teal pull-right">D2</span></span>
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/caresupport/referList.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../caresupport/referList.php">
              <i class="fa fa-ambulance"></i> ตาราง ส่งต่อ
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/caresupport_referral.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/caresupport_referral.php">
              <i class="fa fa-ambulance"></i> ตาราง ส่งต่อ
            </a>
          </li>
          <li class="hidden <?php if ($directoryURI == "/caresupport/positiveReferList.php") {
                              echo "active";
                            } else {
                              echo "noactive";
                            } ?>">
            <a href="../caresupport/positiveReferList.php">
              <i class="fa fa-list-ul"></i> List ประวัติการส่งต่อรักษา<span class="pull-right-container"><span class="hidden label label-maroon pull-right">D2</span></span>
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/caresupport_activity_history.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/caresupport_activity_history.php">
              <i class="fa fa-list-ul"></i> ประวัติการดูแล และ สนับสนุน<span class="pull-right-container"><span class="hidden label label-info pull-right">D2</span></span>
            </a>
          </li>
          <li class="<?php if ($directoryURI == "/caresupport/register_self_care.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../caresupport/register_self_care.php">
              <i class="fa fa-user-plus"></i> ลงทะเบียนเคส self care<span class="pull-right-container"><span class="label label-danger pull-right">New</span></span>
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/caresupport/hivinfo.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
              <a href="../caresupport/stis.php">
                <i class="fa fa-search-plus"></i> รายละเอียด HIV
              </a>
            </li> -->
          <!-- <li class="<?php if ($directoryURI == "/caresupport/hivreferal.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
              <a href="../caresupport/stis.php">
                <i class="fa fa-search-plus"></i> ออกใบส่งต่อรักษา HIV
              </a>
            </li> -->
        </ul>
      </li>

      <li class="header <?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                              echo "";
                                                                                            } else {
                                                                                              echo "hidden ";
                                                                                            } ?>">Same Day ART (SDART)</li>
      <li id="samedayartmenu" class="<?= userHasCode([2, 15]) ? "hidden" : "" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                          echo "";
                                                                                                        } else {
                                                                                                          echo "hidden ";
                                                                                                        } ?> treeview <?php if ($first_part == "samedayart") {
                                                                                                                        echo "active";
                                                                                                                      } else {
                                                                                                                        echo "noactive";
                                                                                                                      } ?>">
        <a href="#">
          <i class="fa fa-tablet"></i>
          <span>Same Day ART</span>
          <!-- <span class="pull-right-container">
              <span class="label label-success pull-right">Same Day ART</span>
            </span> -->
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/samedayart/sdart_hospital_request.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../samedayart/sdart_hospital_request.php">
              <i class="fas fa-hospital" style="margin-right:5px;"></i></i> CBO แจ้งขออนุมัติ SameDay ART
            </a>
          </li>
          <!-- <li class="<?php if ($directoryURI == "/samedayart/sdart_lists.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
                <a href="../samedayart/sdart_lists.php">
                <i class="fas fa-list-ul" style="margin-right:5px;"></i> SDART Profile Lists
              </a>
            </li> -->
          <!-- <li class="<?php if ($directoryURI == "/samedayart/sdart_enter_nap.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
                <a href="../samedayart/pep_enter_nap.php">
                <i class="fas fa-list-ul" style="margin-right:5px;"></i> PEP Visit Enter NAP
              </a>
            </li> -->
          <!-- <li class="<?php if ($directoryURI == "/samedayart/pep_enter_lab.php") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
                <a href="../samedayart/pep_enter_lab.php">
                <i class="fas fa-list-ul" style="margin-right:5px;"></i> PEP Enter Out-Lab
              </a>
            </li> -->
          <li class="<?php if ($directoryURI == "/samedayart/sdart_stock.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../samedayart/sdart_stock.php">
              <i class="fas fa-layer-group" style="margin-right:5px;"></i> ARV Stock
            </a>
          </li>
        </ul>
      </li>

      <li class="header <?= userHasCode(15) ? "" : "hidden" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                          echo "";
                                                                                        } else {
                                                                                          echo "hidden ";
                                                                                        } ?>">eCascade</li>
      <li id="ecascade" class="<?= userHasCode(15) ? "" : "hidden" ?> <?php if (!in_array($_SESSION['user'], [])) {
                                                                                                  echo "";
                                                                                                } else {
                                                                                                  echo "hidden ";
                                                                                                } ?> treeview <?php if ($first_part == "ecascade") {
                                                                                                                echo "active";
                                                                                                              } else {
                                                                                                                echo "noactive";
                                                                                                              } ?>">
        <a href="#">
          <i class="fa fa-area-chart"></i>
          <span>eCascade</span>
          <span class="pull-right-container">
            <span class="label label-success pull-right">eCascade</span>
          </span>
        </a>
        <ul class="treeview-menu">
          <li class="<?php if ($directoryURI == "/ecascade/data_export.php") {
                        echo "active";
                      } else {
                        echo "noactive";
                      } ?>">
            <a href="../ecascade/data_export.php">
              <i class="fa fa-download"></i> open-eCascade export
            </a>
          </li>
        </ul>
      </li>

      <!-- <li class="<?php if ($_SESSION['user'] == "อภิวัฒน์") {
                        echo "";
                      } else {
                        echo "hidden ";
                      } ?><?php if ($directoryURI == "/pages/UI/icons.html") {
                            echo "active";
                          } else {
                            echo "noactive";
                          } ?>">
          <a href="../pages/UI/icons.html">
            <i class="fa fa-info-circle"></i> <span>Icons</span>
            <span class="pull-right-container">
              <small class="label pull-right bg-primary">info</small>
            </span>
          </a>
        </li> -->

    </ul>
  </section>
  <!-- /.sidebar -->
</aside>
